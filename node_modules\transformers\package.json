{"name": "transformers", "version": "2.1.0", "description": "String/Data transformations for use in templating libraries, static site generators and web frameworks", "main": "lib/transformers.js", "scripts": {"pretest": "node test/update-package && npm install", "test": "mocha test/test.js -R spec"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/transformers.git"}, "author": "ForbesLindesay", "license": "MIT", "readmeFilename": "README.md", "gitHead": "4b46e72cba3ad3403fd5ed3802d5472dcfa77311", "devDependencies": {"mocha": "~1.8", "expect.js": "~0.2", "swig": "*", "atpl": "*", "liquor": "*", "ejs": "*", "eco": "*", "jqtpl": "*", "hamljs": "*", "haml-coffee": "*", "whiskers": "*", "hogan.js": "*", "handlebars": "*", "underscore": "*", "walrus": "*", "mustache": "*", "mote": "*", "toffee": "*", "just": "*", "ect": "*", "jade": "*", "then-jade": "*", "dust": "*", "dustjs-linkedin": "*", "jazz": "*", "qejs": "*", "less": "*", "stylus": "*", "sass": "*", "marked": "*", "supermarked": "*", "markdown-js": "*", "markdown": "*", "coffee-script": "*", "cson": "*", "coffeekup": "*", "coffeecup": "*", "templayed": "*", "plates": "*", "dot": "*", "component-builder": "*", "html2jade": "*", "highlight.js": "*"}, "dependencies": {"promise": "~2.0", "css": "~1.0.8", "uglify-js": "~2.2.5"}}
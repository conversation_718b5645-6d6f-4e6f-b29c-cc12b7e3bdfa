const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, '../data/dataase.db');

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('SQLite 连接失败:', err.message);
    } else {
        console.log('SQLite 连接成功');
        initDatabase();
    }
});

// 初始化数据库表
function initDatabase() {
    const createTableSQL = `
        CREATE TABLE IF NOT EXISTS verification_codes (
            email TEXT PRIMARY KEY,
            code TEXT NOT NULL,
            created_at INTEGER NOT NULL,
            expires_at INTEGER NOT NULL
        )
    `;
    
    db.run(createTableSQL, (err) => {
        if (err) {
            console.error('创建验证码表失败:', err.message);
        } else {
            console.log('验证码表初始化成功');
        }
    });
}

// 保存验证码
function saveVerificationCode(email, code, expireMinutes = 5) {
    return new Promise((resolve, reject) => {
        const now = Date.now();
        const expiresAt = now + (expireMinutes * 60 * 1000); // 5分钟后过期
        
        const sql = `
            INSERT OR REPLACE INTO verification_codes 
            (email, code, created_at, expires_at) 
            VALUES (?, ?, ?, ?)
        `;
        
        db.run(sql, [email, code, now, expiresAt], function(err) {
            if (err) {
                console.error('保存验证码失败:', err.message);
                reject(err);
            } else {
                console.log(`验证码已保存: ${email} -> ${code}`);
                resolve({ success: true, message: '验证码保存成功' });
            }
        });
    });
}

// 验证验证码
function verifyCode(email, inputCode) {
    return new Promise((resolve, reject) => {
        const now = Date.now();
        
        const sql = `
            SELECT code, expires_at FROM verification_codes 
            WHERE email = ? AND expires_at > ?
        `;
        
        db.get(sql, [email, now], (err, row) => {
            if (err) {
                console.error('查询验证码失败:', err.message);
                reject(err);
                return;
            }
            
            if (!row) {
                resolve({ success: false, message: '验证码不存在或已过期' });
                return;
            }
            
            if (row.code !== inputCode) {
                resolve({ success: false, message: '验证码错误' });
                return;
            }
            
            // 验证成功，删除验证码（一次性使用）
            deleteVerificationCode(email);
            resolve({ success: true, message: '验证成功' });
        });
    });
}

// 删除验证码
function deleteVerificationCode(email) {
    const sql = `DELETE FROM verification_codes WHERE email = ?`;
    
    db.run(sql, [email], (err) => {
        if (err) {
            console.error('删除验证码失败:', err.message);
        } else {
            console.log(`验证码已删除: ${email}`);
        }
    });
}

// 清理过期验证码
function cleanExpiredCodes() {
    const now = Date.now();
    const sql = `DELETE FROM verification_codes WHERE expires_at <= ?`;
    
    db.run(sql, [now], function(err) {
        if (err) {
            console.error('清理过期验证码失败:', err.message);
        } else {
            console.log(`清理了 ${this.changes} 个过期验证码`);
        }
    });
}

// 定时清理过期验证码（每分钟执行一次）
setInterval(cleanExpiredCodes, 60 * 1000);

// 关闭数据库连接
function closeDatabase() {
    db.close((err) => {
        if (err) {
            console.error('关闭数据库失败:', err.message);
        } else {
            console.log('数据库连接已关闭');
        }
    });
}

module.exports = {
    saveVerificationCode,
    verifyCode,
    deleteVerificationCode,
    cleanExpiredCodes,
    closeDatabase
};  

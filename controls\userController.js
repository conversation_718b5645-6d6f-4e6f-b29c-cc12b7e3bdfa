const { query } = require('../config/database');
const { verifyCode } = require('../utils/sqliteHelper');

// 生成随机用户名
function generateRandomUsername() {
    const prefixes = [
        '书适', '适读', '圈友', '书圈', '适圈', '读适',
        '书适客', '适书者', '圈内人', '书适控', '适读家', '圈中人',
        '书适星', '适圈达人', '书圈客', '适读控', '圈书友', '书适族'
    ];
    
    const suffixes = [
        Math.floor(Math.random() * 10000).toString().padStart(4, '0'),
        Math.floor(Math.random() * 100000).toString().padStart(5, '0'),
        Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    ];
    
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
    
    return prefix + suffix;
}

// 检查用户名是否已存在
async function checkUsernameExists(username) {
    try {
        const result = await query('SELECT id FROM users WHERE username = ?', [username]);
        return result.length > 0;
    } catch (error) {
        throw error;
    }
}

// 检查邮箱是否已存在
async function checkEmailExists(email) {
    try {
        const result = await query('SELECT id FROM users WHERE email = ?', [email]);
        return result.length > 0;
    } catch (error) {
        throw error;
    }
}

// 生成唯一用户名
async function generateUniqueUsername() {
    let username;
    let exists = true;
    let attempts = 0;
    const maxAttempts = 10;
    
    while (exists && attempts < maxAttempts) {
        username = generateRandomUsername();
        exists = await checkUsernameExists(username);
        attempts++;
    }
    
    if (exists) {
        // 如果还是重复，加上时间戳
        username = generateRandomUsername() + Date.now().toString().slice(-4);
    }
    
    return username;
}

// 用户注册
const register = async (req, res) => {
    const { email, password, code } = req.body;
    
    // 验证参数
    if (!email || !password || !code) {
        return res.json({
            code: 400,
            message: '邮箱、密码和验证码不能为空'
        });
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return res.json({
            code: 400,
            message: '邮箱格式不正确'
        });
    }
    
    // 验证密码长度
    if (password.length < 6) {
        return res.json({
            code: 400,
            message: '密码长度不能少于6位'
        });
    }
    
    try {
        // 1. 验证验证码
        const codeResult = await verifyCode(email, code);
        if (!codeResult.success) {
            return res.json({
                code: 400,
                message: codeResult.message
            });
        }
        
        // 2. 检查邮箱是否已注册
        const emailExists = await checkEmailExists(email);
        if (emailExists) {
            return res.json({
                code: 400,
                message: '该邮箱已被注册'
            });
        }
        
        // 3. 生成唯一用户名
        const username = await generateUniqueUsername();
        
        // 4. 插入用户数据
        const insertSQL = `
            INSERT INTO users (username, email, password, role, status) 
            VALUES (?, ?, ?, '普通用户', 1)
        `;
        
        const result = await query(insertSQL, [username, email, password]);
        
        if (result.affectedRows > 0) {
            res.json({
                code: 200,
                message: '注册成功',
                data: {
                    id: result.insertId,
                    username: username,
                    email: email,
                    role: '普通用户'
                }
            });
        } else {
            res.json({
                code: 500,
                message: '注册失败，请重试'
            });
        }
        
    } catch (error) {
        console.error('注册失败:', error);
        res.json({
            code: 500,
            message: '注册失败',
            error: error.message
        });
    }
};

module.exports = {
    register
};

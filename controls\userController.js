const { query } = require('../config/database');
const { verifyCode } = require('../utils/sqliteHelper');
const { generateToken, verifyToken } = require('../utils/tokenHelper');
const fs = require('fs');
const path = require('path');

// 生成随机用户名
function generateRandomUsername() {
    const prefixes = [
        '书适', '适读', '圈友', '书圈', '适圈', '读适',
        '书适客', '适书者', '圈内人', '书适控', '适读家', '圈中人',
        '书适星', '适圈达人', '书圈客', '适读控', '圈书友', '书适族'
    ];
    
    const suffixes = [
        Math.floor(Math.random() * 10000).toString().padStart(4, '0'),
        Math.floor(Math.random() * 100000).toString().padStart(5, '0'),
        Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    ];
    
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
    
    return prefix + suffix;
}

// 检查用户名是否已存在
async function checkUsernameExists(username) {
    try {
        const result = await query('SELECT id FROM users WHERE username = ?', [username]);
        return result.length > 0;
    } catch (error) {
        throw error;
    }
}

// 检查邮箱是否已存在
async function checkEmailExists(email) {
    try {
        const result = await query('SELECT id FROM users WHERE email = ?', [email]);
        return result.length > 0;
    } catch (error) {
        throw error;
    }
}

// 生成唯一用户名
async function generateUniqueUsername() {
    let username;
    let exists = true;
    let attempts = 0;
    const maxAttempts = 10;
    
    while (exists && attempts < maxAttempts) {
        username = generateRandomUsername();
        exists = await checkUsernameExists(username);
        attempts++;
    }
    
    if (exists) {
        // 如果还是重复，加上时间戳
        username = generateRandomUsername() + Date.now().toString().slice(-4);
    }
    
    return username;
}

// 用户注册
const register = async (req, res) => {
    const { email, password, code } = req.body;
    
    // 验证参数
    if (!email || !password || !code) {
        return res.json({
            code: 400,
            message: '邮箱、密码和验证码不能为空'
        });
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return res.json({
            code: 400,
            message: '邮箱格式不正确'
        });
    }
    
    // 验证密码长度
    if (password.length < 6) {
        return res.json({
            code: 400,
            message: '密码长度不能少于6位'
        });
    }
    
    try {
        // 1. 验证验证码
        const codeResult = await verifyCode(email, code);
        if (!codeResult.success) {
            return res.json({
                code: 400,
                message: codeResult.message
            });
        }
        
        // 2. 检查邮箱是否已注册
        const emailExists = await checkEmailExists(email);
        if (emailExists) {
            return res.json({
                code: 400,
                message: '该邮箱已被注册'
            });
        }
        
        // 3. 生成唯一用户名
        const username = await generateUniqueUsername();
        
        // 4. 插入用户数据
        const insertSQL = `
            INSERT INTO users (username, email, password, role, status) 
            VALUES (?, ?, ?, '普通用户', 1)
        `;
        
        const result = await query(insertSQL, [username, email, password]);
        
        if (result.affectedRows > 0) {
            res.json({
                code: 200,
                message: '注册成功',
                data: {
                    id: result.insertId,
                    username: username,
                    email: email,
                    role: '普通用户'
                }
            });
        } else {
            res.json({
                code: 500,
                message: '注册失败，请重试'
            });
        }
        
    } catch (error) {
        console.error('注册失败:', error);
        res.json({
            code: 500,
            message: '注册失败',
            error: error.message
        });
    }
};

// 用户登录
const login = async (req, res) => {
    const { loginType, email, username, password, code } = req.body;

    // 验证登录类型
    if (!loginType || !['email_code', 'email_password', 'username_password'].includes(loginType)) {
        return res.json({
            code: 400,
            message: '登录类型无效，支持: email_code, email_password, username_password'
        });
    }

    try {
        let user = null;

        // 1. 邮箱验证码登录
        if (loginType === 'email_code') {
            if (!email || !code) {
                return res.json({
                    code: 400,
                    message: '邮箱和验证码不能为空'
                });
            }

            // 验证验证码
            const codeResult = await verifyCode(email, code);
            if (!codeResult.success) {
                return res.json({
                    code: 400,
                    message: codeResult.message
                });
            }

            // 查询用户
            const users = await query('SELECT * FROM users WHERE email = ? AND status = 1', [email]);
            if (users.length === 0) {
                return res.json({
                    code: 400,
                    message: '用户不存在或账号已被禁用'
                });
            }
            user = users[0];
        }

        // 2. 邮箱密码登录
        else if (loginType === 'email_password') {
            if (!email || !password) {
                return res.json({
                    code: 400,
                    message: '邮箱和密码不能为空'
                });
            }

            // 查询用户并验证密码
            const users = await query('SELECT * FROM users WHERE email = ? AND password = ? AND status = 1', [email, password]);
            if (users.length === 0) {
                return res.json({
                    code: 400,
                    message: '邮箱或密码错误，或账号已被禁用'
                });
            }
            user = users[0];
        }

        // 3. 用户名密码登录
        else if (loginType === 'username_password') {
            if (!username || !password) {
                return res.json({
                    code: 400,
                    message: '用户名和密码不能为空'
                });
            }

            // 查询用户并验证密码
            const users = await query('SELECT * FROM users WHERE username = ? AND password = ? AND status = 1', [username, password]);
            if (users.length === 0) {
                return res.json({
                    code: 400,
                    message: '用户名或密码错误，或账号已被禁用'
                });
            }
            user = users[0];
        }

        // 4. 更新最后登录时间
        await query('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

        // 5. 生成token
        const token = generateToken({
            id: user.id,
            username: user.username,
            email: user.email
        });

        // 6. 返回登录成功信息
        res.json({
            code: 200,
            message: '登录成功',
            data: {
                token: token,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    avatar: user.avatar,
                    role: user.role,
                    created_at: user.created_at,
                    last_login: user.last_login
                }
            }
        });

    } catch (error) {
        console.error('登录失败:', error);
        res.json({
            code: 500,
            message: '登录失败',
            error: error.message
        });
    }
};

// 根据token获取用户信息
const getUserInfo = async (req, res) => {
    const { token } = req.body;

    // 验证token参数
    if (!token) {
        return res.json({
            code: 400,
            message: 'token不能为空'
        });
    }

    try {
        // 1. 验证token
        const tokenResult = verifyToken(token);
        if (!tokenResult.success) {
            return res.json({
                code: 401,
                message: tokenResult.message
            });
        }

        // 2. 从token中获取用户ID
        const userId = tokenResult.data.id;

        // 3. 查询用户完整信息
        const users = await query('SELECT * FROM users WHERE id = ? AND status = 1', [userId]);
        if (users.length === 0) {
            return res.json({
                code: 404,
                message: '用户不存在或账号已被禁用'
            });
        }

        const user = users[0];

        // 4. 返回用户信息（不包含密码）
        res.json({
            code: 200,
            message: '获取用户信息成功',
            data: {
                id: user.id,
                username: user.username,
                email: user.email,
                avatar: user.avatar,
                role: user.role,
                status: user.status,
                created_at: user.created_at,
                updated_at: user.updated_at,
                last_login: user.last_login,
                token_info: {
                    issued_at: new Date(tokenResult.data.iat * 1000).toLocaleString(),
                    expires_at: new Date(tokenResult.data.exp * 1000).toLocaleString()
                }
            }
        });

    } catch (error) {
        console.error('获取用户信息失败:', error);
        res.json({
            code: 500,
            message: '获取用户信息失败',
            error: error.message
        });
    }
};

// 更新用户头像
const updateAvatar = async (req, res) => {
    const { token, avatar } = req.body;

    // 验证参数
    if (!token) {
        return res.json({
            code: 400,
            message: 'token不能为空'
        });
    }

    if (!avatar) {
        return res.json({
            code: 400,
            message: '头像数据不能为空'
        });
    }

    try {
        // 1. 验证token
        const tokenResult = verifyToken(token);
        if (!tokenResult.success) {
            return res.json({
                code: 401,
                message: tokenResult.message
            });
        }

        // 2. 从token中获取用户ID
        const userId = tokenResult.data.id;

        // 3. 验证用户是否存在
        const users = await query('SELECT id FROM users WHERE id = ? AND status = 1', [userId]);
        if (users.length === 0) {
            return res.json({
                code: 404,
                message: '用户不存在或账号已被禁用'
            });
        }

        // 4. 创建头像目录
        const avatarDir = path.join(__dirname, '../public/images/头像', userId.toString());
        if (!fs.existsSync(avatarDir)) {
            fs.mkdirSync(avatarDir, { recursive: true });
        }

        // 5. 处理base64头像数据
        let base64Data = avatar;
        if (avatar.startsWith('data:image/')) {
            // 移除data:image/png;base64,前缀
            base64Data = avatar.split(',')[1];
        }

        // 6. 保存头像文件
        const avatarPath = path.join(avatarDir, 'avatar.png');
        fs.writeFileSync(avatarPath, base64Data, 'base64');

        // 7. 更新数据库中的头像路径
        const dbAvatarPath = `/images/头像/${userId}/avatar.png`;
        await query('UPDATE users SET avatar = ?, updated_at = NOW() WHERE id = ?', [dbAvatarPath, userId]);

        // 8. 返回成功结果
        res.json({
            code: 200,
            message: '头像更新成功',
            data: {
                userId: userId,
                avatarPath: dbAvatarPath,
                avatarUrl: `http://localhost:3000${dbAvatarPath}`
            }
        });

    } catch (error) {
        console.error('更新头像失败:', error);
        res.json({
            code: 500,
            message: '更新头像失败',
            error: error.message
        });
    }
};

// 修改用户信息
const updateUserInfo = async (req, res) => {
    const { token, username, email } = req.body;

    // 验证参数
    if (!token) {
        return res.json({
            code: 400,
            message: 'token不能为空'
        });
    }

    if (!username && !email) {
        return res.json({
            code: 400,
            message: '至少需要提供用户名或邮箱中的一个'
        });
    }

    try {
        // 1. 验证token
        const tokenResult = verifyToken(token);
        if (!tokenResult.success) {
            return res.json({
                code: 401,
                message: tokenResult.message
            });
        }

        // 2. 从token中获取用户ID
        const userId = tokenResult.data.id;

        // 3. 验证用户是否存在
        const users = await query('SELECT id FROM users WHERE id = ? AND status = 1', [userId]);
        if (users.length === 0) {
            return res.json({
                code: 404,
                message: '用户不存在或账号已被禁用'
            });
        }

        // 4. 检查用户名是否已被其他用户使用
        if (username) {
            const existingUsers = await query('SELECT id FROM users WHERE username = ? AND id != ?', [username, userId]);
            if (existingUsers.length > 0) {
                return res.json({
                    code: 400,
                    message: '用户名已被其他用户使用'
                });
            }
        }

        // 5. 检查邮箱是否已被其他用户使用
        if (email) {
            const existingUsers = await query('SELECT id FROM users WHERE email = ? AND id != ?', [email, userId]);
            if (existingUsers.length > 0) {
                return res.json({
                    code: 400,
                    message: '邮箱已被其他用户使用'
                });
            }
        }

        // 6. 构建更新SQL
        let updateFields = [];
        let updateValues = [];

        if (username) {
            updateFields.push('username = ?');
            updateValues.push(username);
        }

        if (email) {
            updateFields.push('email = ?');
            updateValues.push(email);
        }

        updateFields.push('updated_at = NOW()');
        updateValues.push(userId);

        const updateSQL = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;

        // 7. 执行更新
        await query(updateSQL, updateValues);

        // 8. 获取更新后的用户信息
        const updatedUsers = await query('SELECT id, username, email, avatar, role, status, created_at, updated_at, last_login FROM users WHERE id = ?', [userId]);
        const updatedUser = updatedUsers[0];

        // 9. 返回成功结果
        res.json({
            code: 200,
            message: '用户信息更新成功',
            data: {
                user: updatedUser
            }
        });

    } catch (error) {
        console.error('更新用户信息失败:', error);
        res.json({
            code: 500,
            message: '更新用户信息失败',
            error: error.message
        });
    }
};

// 修改密码
const updatePassword = async (req, res) => {
    const { token, oldPassword, newPassword } = req.body;

    // 验证参数
    if (!token) {
        return res.json({
            code: 400,
            message: 'token不能为空'
        });
    }

    if (!oldPassword || !newPassword) {
        return res.json({
            code: 400,
            message: '旧密码和新密码不能为空'
        });
    }

    if (newPassword.length < 6) {
        return res.json({
            code: 400,
            message: '新密码长度不能少于6位'
        });
    }

    if (oldPassword === newPassword) {
        return res.json({
            code: 400,
            message: '新密码不能与旧密码相同'
        });
    }

    try {
        // 1. 验证token
        const tokenResult = verifyToken(token);
        if (!tokenResult.success) {
            return res.json({
                code: 401,
                message: tokenResult.message
            });
        }

        // 2. 从token中获取用户ID
        const userId = tokenResult.data.id;

        // 3. 验证用户是否存在并检查旧密码
        const users = await query('SELECT id, password FROM users WHERE id = ? AND status = 1', [userId]);
        if (users.length === 0) {
            return res.json({
                code: 404,
                message: '用户不存在或账号已被禁用'
            });
        }

        const user = users[0];
        if (user.password !== oldPassword) {
            return res.json({
                code: 400,
                message: '旧密码错误'
            });
        }

        // 4. 更新密码
        await query('UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?', [newPassword, userId]);

        // 5. 返回成功结果
        res.json({
            code: 200,
            message: '密码修改成功',
            data: {
                userId: userId,
                message: '密码已更新，建议重新登录'
            }
        });

    } catch (error) {
        console.error('修改密码失败:', error);
        res.json({
            code: 500,
            message: '修改密码失败',
            error: error.message
        });
    }
};

module.exports = {
    register,
    login,
    getUserInfo,
    updateAvatar,
    updateUserInfo,
    updatePassword
};

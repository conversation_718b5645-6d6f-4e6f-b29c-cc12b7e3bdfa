// 验证码内存存储工具
const codeStorage = new Map();

// 存储验证码
function saveCode(email, code, expireTime = 5 * 60 * 1000) { // 默认5分钟过期
    const expireAt = Date.now() + expireTime;
    codeStorage.set(email, {
        code: code,
        expireAt: expireAt,
        createTime: Date.now()
    });
    
    // 设置定时清理
    setTimeout(() => {
        codeStorage.delete(email);
    }, expireTime);
}

// 验证验证码
function verifyCode(email, inputCode) {
    const stored = codeStorage.get(email);
    
    if (!stored) {
        return { success: false, message: '验证码不存在或已过期' };
    }
    
    if (Date.now() > stored.expireAt) {
        codeStorage.delete(email);
        return { success: false, message: '验证码已过期' };
    }
    
    if (stored.code !== inputCode) {
        return { success: false, message: '验证码错误' };
    }
    
    // 验证成功后删除验证码（一次性使用）
    codeStorage.delete(email);
    return { success: true, message: '验证成功' };
}

// 清理过期验证码
function cleanExpiredCodes() {
    const now = Date.now();
    for (const [email, data] of codeStorage.entries()) {
        if (now > data.expireAt) {
            codeStorage.delete(email);
        }
    }
}

// 定时清理过期验证码（每分钟执行一次）
setInterval(cleanExpiredCodes, 60 * 1000);

module.exports = {
    saveCode,
    verifyCode,
    cleanExpiredCodes
};

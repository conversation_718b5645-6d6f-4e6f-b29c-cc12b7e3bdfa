{"name": "longest", "description": "Get the longest item in an array.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/longest", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/longest.git"}, "bugs": {"url": "https://github.com/jonschlinkert/longest/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/longest/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"fill-range": "^2.1.0", "mocha": "*"}, "keywords": ["array", "element", "item", "long", "length", "longest"]}
# longest [![NPM version](https://badge.fury.io/js/longest.svg)](http://badge.fury.io/js/longest)  [![Build Status](https://travis-ci.org/jonschlinkert/longest.svg)](https://travis-ci.org/jonschlinkert/longest) 

> Get the longest item in an array.

## Install with [npm](npmjs.org)

```bash
npm i longest --save
```
### Install with [bower](https://github.com/bower/bower)

```bash
bower install longest --save
```

## Running tests
Install dev dependencies.

```bash
npm i -d && npm test
```

## Usage

```js
var longest = require('longest');
longest(['a', 'abcde', 'abc']);
//=> 'abcde'

longest(['a', 'abcde', 'abc']).length;
//=> 5
```

## Related projects
* [longest-value](https://github.com/jonschlinkert/longest-value): Get the longest value for the given property from an array of objects. Useful for aligning values.
* [right-align-values](https://github.com/jonschlinkert/right-align-values): Right align the values of a given property for each object in an array. Useful for creating text columns or tables.
* [right-pad-values](https://github.com/jonschlinkert/right-pad-values): Right pad the values of a given property for each object in an array. Useful for creating text columns or tables.
* [repeat-string](https://github.com/jonschlinkert/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string.
* [pad-right](https://github.com/jonschlinkert/pad-right): Right pad a string with zeros or a specified string. Fastest implementation.
* [pad-left](https://github.com/jonschlinkert/pad-left): Left pad a string with zeros or a specified string. Fastest implementation.  

## Running tests
Install dev dependencies.

```bash
npm i -d && npm test
```

## Contributing
Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/longest/issues)

## Author

**Jon Schlinkert**
 
+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert) 

## License
Copyright (c) 2015 Jon Schlinkert  
Released under the MIT license

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on March 31, 2015._
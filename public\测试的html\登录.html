<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试登录接口</title>
</head>
<body>
    <h1>测试登录接口</h1>

    <!-- 发送验证码 -->
    <div>
        <h2>1. 发送验证码（用于邮箱验证码登录）</h2>
        <form id="emailForm">
            <div>
                <label for="email">邮箱地址:</label>
                <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
            </div>
            <br>
            <button type="submit">发送验证码</button>
        </form>

        <div id="emailResult">
            <h3>验证码响应结果:</h3>
            <pre id="emailResponse"></pre>
        </div>
    </div>

    <hr>

    <!-- 邮箱验证码登录 -->
    <div>
        <h2>2. 邮箱验证码登录</h2>
        <form id="emailCodeLoginForm">
            <div>
                <label for="loginEmail1">邮箱地址:</label>
                <input type="email" id="loginEmail1" name="loginEmail1" placeholder="请输入邮箱地址" required>
            </div>
            <br>
            <div>
                <label for="loginCode">验证码:</label>
                <input type="text" id="loginCode" name="loginCode" placeholder="请输入验证码" required>
            </div>
            <br>
            <button type="submit">邮箱验证码登录</button>
        </form>

        <div id="emailCodeResult">
            <h3>登录响应结果:</h3>
            <pre id="emailCodeResponse"></pre>
        </div>
    </div>

    <hr>

    <!-- 邮箱密码登录 -->
    <div>
        <h2>3. 邮箱密码登录</h2>
        <form id="emailPasswordLoginForm">
            <div>
                <label for="loginEmail2">邮箱地址:</label>
                <input type="email" id="loginEmail2" name="loginEmail2" placeholder="请输入邮箱地址" required>
            </div>
            <br>
            <div>
                <label for="loginPassword1">密码:</label>
                <input type="password" id="loginPassword1" name="loginPassword1" placeholder="请输入密码" required>
            </div>
            <br>
            <button type="submit">邮箱密码登录</button>
        </form>

        <div id="emailPasswordResult">
            <h3>登录响应结果:</h3>
            <pre id="emailPasswordResponse"></pre>
        </div>
    </div>

    <hr>

    <!-- 用户名密码登录 -->
    <div>
        <h2>4. 用户名密码登录</h2>
        <form id="usernamePasswordLoginForm">
            <div>
                <label for="loginUsername">用户名:</label>
                <input type="text" id="loginUsername" name="loginUsername" placeholder="请输入用户名" required>
            </div>
            <br>
            <div>
                <label for="loginPassword2">密码:</label>
                <input type="password" id="loginPassword2" name="loginPassword2" placeholder="请输入密码" required>
            </div>
            <br>
            <button type="submit">用户名密码登录</button>
        </form>

        <div id="usernamePasswordResult">
            <h3>登录响应结果:</h3>
            <pre id="usernamePasswordResponse"></pre>
        </div>
    </div>

    <script>
        // 发送验证码
        document.getElementById('emailForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const responseDiv = document.getElementById('emailResponse');

            if (!email) {
                responseDiv.textContent = '请输入邮箱地址';
                return;
            }

            try {
                responseDiv.textContent = '发送中...';

                const response = await fetch('http://localhost:3000/email/getCode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        qq: email
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('验证码发送成功！请查收邮件');
                    // 自动填充登录表单的邮箱
                    document.getElementById('loginEmail1').value = email;
                } else {
                    alert('发送失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });

        // 邮箱验证码登录
        document.getElementById('emailCodeLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail1').value;
            const code = document.getElementById('loginCode').value;
            const responseDiv = document.getElementById('emailCodeResponse');

            if (!email || !code) {
                responseDiv.textContent = '请填写邮箱和验证码';
                return;
            }

            try {
                responseDiv.textContent = '登录中...';

                const response = await fetch('http://localhost:3000/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        loginType: 'email_code',
                        email: email,
                        code: code
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('登录成功！用户名: ' + data.data.user.username);
                    localStorage.setItem('token', data.data.token);
                } else {
                    alert('登录失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });

        // 邮箱密码登录
        document.getElementById('emailPasswordLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail2').value;
            const password = document.getElementById('loginPassword1').value;
            const responseDiv = document.getElementById('emailPasswordResponse');

            if (!email || !password) {
                responseDiv.textContent = '请填写邮箱和密码';
                return;
            }

            try {
                responseDiv.textContent = '登录中...';

                const response = await fetch('http://localhost:3000/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        loginType: 'email_password',
                        email: email,
                        password: password
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('登录成功！用户名: ' + data.data.user.username);
                    localStorage.setItem('token', data.data.token);
                } else {
                    alert('登录失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });

        // 用户名密码登录
        document.getElementById('usernamePasswordLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword2').value;
            const responseDiv = document.getElementById('usernamePasswordResponse');

            if (!username || !password) {
                responseDiv.textContent = '请填写用户名和密码';
                return;
            }

            try {
                responseDiv.textContent = '登录中...';

                const response = await fetch('http://localhost:3000/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        loginType: 'username_password',
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('登录成功！用户名: ' + data.data.user.username);
                    localStorage.setItem('token', data.data.token);
                } else {
                    alert('登录失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
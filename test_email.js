// 测试邮件发送接口
const axios = require('axios');

async function testEmailAPI() {
    try {
        const response = await axios.post('http://localhost:3000/email/getCode', {
            qq: '测试邮箱@qq.com'  // 替换为实际的测试邮箱
        });
        
        console.log('接口响应:', response.data);
    } catch (error) {
        console.error('测试失败:', error.response ? error.response.data : error.message);
    }
}

// 运行测试
testEmailAPI();

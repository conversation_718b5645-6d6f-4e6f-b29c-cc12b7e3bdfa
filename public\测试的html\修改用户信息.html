<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修改用户信息接口</title>
</head>
<body>
    <h1>测试修改用户信息接口</h1>
    
    <!-- 修改用户信息 -->
    <div>
        <h2>1. 修改用户信息</h2>
        <form id="updateUserInfoForm">
            <div>
                <label for="token1">Token:</label>
                <br>
                <textarea id="token1" name="token1" rows="2" cols="80" placeholder="请输入JWT token" required></textarea>
            </div>
            <br>
            
            <div>
                <label for="username">新用户名:</label>
                <input type="text" id="username" name="username" placeholder="请输入新用户名（可选）">
            </div>
            <br>
            
            <div>
                <label for="email">新邮箱:</label>
                <input type="email" id="email" name="email" placeholder="请输入新邮箱（可选）">
            </div>
            <br>
            
            <button type="submit">修改用户信息</button>
        </form>
        
        <div id="userInfoResult">
            <h3>响应结果:</h3>
            <pre id="userInfoResponse"></pre>
        </div>
    </div>
    
    <hr>
    
    <!-- 修改密码 -->
    <div>
        <h2>2. 修改密码</h2>
        <form id="updatePasswordForm">
            <div>
                <label for="token2">Token:</label>
                <br>
                <textarea id="token2" name="token2" rows="2" cols="80" placeholder="请输入JWT token" required></textarea>
            </div>
            <br>
            
            <div>
                <label for="oldPassword">旧密码:</label>
                <input type="password" id="oldPassword" name="oldPassword" placeholder="请输入旧密码" required>
            </div>
            <br>
            
            <div>
                <label for="newPassword">新密码:</label>
                <input type="password" id="newPassword" name="newPassword" placeholder="请输入新密码（至少6位）" required>
            </div>
            <br>
            
            <button type="submit">修改密码</button>
        </form>
        
        <div id="passwordResult">
            <h3>响应结果:</h3>
            <pre id="passwordResponse"></pre>
        </div>
    </div>

    <script>
        // 修改用户信息
        document.getElementById('updateUserInfoForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('token1').value.trim();
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const responseDiv = document.getElementById('userInfoResponse');
            
            if (!token) {
                responseDiv.textContent = '请输入token';
                return;
            }
            
            if (!username && !email) {
                responseDiv.textContent = '请至少填写用户名或邮箱中的一个';
                return;
            }
            
            try {
                responseDiv.textContent = '更新中...';
                
                const requestBody = { token };
                if (username) requestBody.username = username;
                if (email) requestBody.email = email;
                
                const response = await fetch('http://localhost:3000/user/updateUserInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.code === 200) {
                    alert('用户信息修改成功！');
                } else {
                    alert('修改失败: ' + data.message);
                }
                
            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });
        
        // 修改密码
        document.getElementById('updatePasswordForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('token2').value.trim();
            const oldPassword = document.getElementById('oldPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const responseDiv = document.getElementById('passwordResponse');
            
            if (!token) {
                responseDiv.textContent = '请输入token';
                return;
            }
            
            if (!oldPassword || !newPassword) {
                responseDiv.textContent = '请填写旧密码和新密码';
                return;
            }
            
            if (newPassword.length < 6) {
                responseDiv.textContent = '新密码长度不能少于6位';
                return;
            }
            
            if (oldPassword === newPassword) {
                responseDiv.textContent = '新密码不能与旧密码相同';
                return;
            }
            
            try {
                responseDiv.textContent = '修改中...';
                
                const response = await fetch('http://localhost:3000/user/updatePassword', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: token,
                        oldPassword: oldPassword,
                        newPassword: newPassword
                    })
                });
                
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.code === 200) {
                    alert('密码修改成功！建议重新登录');
                    // 清空密码输入框
                    document.getElementById('oldPassword').value = '';
                    document.getElementById('newPassword').value = '';
                } else {
                    alert('修改失败: ' + data.message);
                }
                
            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });
    </script>
</body>
</html>

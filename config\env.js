// 环境配置文件
const env = {
  development: {
    database: {
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'shushiquan_db',
      port: 3306
    },
    server: {
      port: 3000
    }
  },
  production: {
    database: {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'shushiquan_db',
      port: process.env.DB_PORT || 3306
    },
    server: {
      port: process.env.PORT || 3000
    }
  }
};

const currentEnv = process.env.NODE_ENV || 'development';

module.exports = env[currentEnv];

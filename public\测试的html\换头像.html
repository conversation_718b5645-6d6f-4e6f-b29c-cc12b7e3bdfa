<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试更换头像接口</title>
</head>
<body>
    <h1>测试更换头像接口</h1>

    <!-- 显示当前存储的token -->
    <div>
        <h2>当前存储的Token</h2>
        <div>
            <label>localStorage中的token:</label>
            <br>
            <textarea id="currentToken" rows="2" cols="80" readonly></textarea>
            <br><br>
            <button onclick="loadTokenFromStorage()">从localStorage加载Token</button>
            <button onclick="clearToken()">清除Token</button>
        </div>
    </div>

    <hr>

    <!-- 更换头像 -->
    <div>
        <h2>更换头像</h2>
        <form id="updateAvatarForm">
            <div>
                <label for="tokenInput">Token:</label>
                <br>
                <textarea id="tokenInput" name="tokenInput" rows="2" cols="80" placeholder="请输入JWT token" required></textarea>
                <br><br>
                <button type="button" onclick="useCurrentToken()">使用当前存储的Token</button>
            </div>
            <br>

            <div>
                <label for="avatarFile">选择头像文件:</label>
                <br>
                <input type="file" id="avatarFile" name="avatarFile" accept="image/*" required>
                <br><br>
                <div>
                    <label>头像预览:</label>
                    <br>
                    <img id="avatarPreview" src="" alt="头像预览" width="150" height="150" style="border: 1px solid #ccc; display: none;">
                </div>
            </div>
            <br>

            <button type="submit">更新头像</button>
        </form>

        <div id="result">
            <h3>响应结果:</h3>
            <pre id="response"></pre>
        </div>

        <div id="avatarResult" style="display: none;">
            <h3>更新后的头像:</h3>
            <img id="newAvatar" src="" alt="新头像" width="150" height="150" style="border: 1px solid #ccc;">
            <br>
            <p>头像URL: <span id="avatarUrl"></span></p>
        </div>
    </div>

    <hr>

    <!-- 快速登录获取token -->
    <div>
        <h2>快速登录获取Token（用于测试）</h2>
        <form id="quickLoginForm">
            <div>
                <label for="loginEmail">邮箱:</label>
                <input type="email" id="loginEmail" name="loginEmail" placeholder="请输入邮箱" required>
            </div>
            <br>
            <div>
                <label for="loginPassword">密码:</label>
                <input type="password" id="loginPassword" name="loginPassword" placeholder="请输入密码" required>
            </div>
            <br>
            <button type="submit">邮箱密码登录</button>
        </form>

        <div id="loginResult">
            <h3>登录响应结果:</h3>
            <pre id="loginResponse"></pre>
        </div>
    </div>

    <script>
        // 页面加载时显示当前token
        window.onload = function() {
            loadTokenFromStorage();
        };

        // 从localStorage加载token
        function loadTokenFromStorage() {
            const token = localStorage.getItem('token');
            const currentTokenTextarea = document.getElementById('currentToken');

            if (token) {
                currentTokenTextarea.value = token;
            } else {
                currentTokenTextarea.value = '暂无存储的token';
            }
        }

        // 清除token
        function clearToken() {
            localStorage.removeItem('token');
            document.getElementById('currentToken').value = '暂无存储的token';
            document.getElementById('tokenInput').value = '';
            alert('Token已清除');
        }

        // 使用当前存储的token
        function useCurrentToken() {
            const token = localStorage.getItem('token');
            if (token) {
                document.getElementById('tokenInput').value = token;
            } else {
                alert('暂无存储的token，请先登录');
            }
        }

        // 头像文件选择预览
        document.getElementById('avatarFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('avatarPreview');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        });

        // 更新头像
        document.getElementById('updateAvatarForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const token = document.getElementById('tokenInput').value.trim();
            const fileInput = document.getElementById('avatarFile');
            const responseDiv = document.getElementById('response');

            if (!token) {
                responseDiv.textContent = '请输入token';
                return;
            }

            if (!fileInput.files[0]) {
                responseDiv.textContent = '请选择头像文件';
                return;
            }

            try {
                responseDiv.textContent = '上传中...';

                // 将文件转换为base64
                const file = fileInput.files[0];
                const reader = new FileReader();

                reader.onload = async function(e) {
                    const base64Data = e.target.result;

                    try {
                        const response = await fetch('http://localhost:3000/user/updateAvatar', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                token: token,
                                avatar: base64Data
                            })
                        });

                        const data = await response.json();
                        responseDiv.textContent = JSON.stringify(data, null, 2);

                        if (data.code === 200) {
                            alert('头像更新成功！');

                            // 显示新头像
                            const avatarResult = document.getElementById('avatarResult');
                            const newAvatar = document.getElementById('newAvatar');
                            const avatarUrl = document.getElementById('avatarUrl');

                            newAvatar.src = data.data.avatarUrl;
                            avatarUrl.textContent = data.data.avatarUrl;
                            avatarResult.style.display = 'block';
                        } else {
                            alert('更新失败: ' + data.message);
                        }

                    } catch (error) {
                        responseDiv.textContent = '请求失败: ' + error.message;
                        alert('请求失败: ' + error.message);
                    }
                };

                reader.readAsDataURL(file);

            } catch (error) {
                responseDiv.textContent = '处理文件失败: ' + error.message;
                alert('处理文件失败: ' + error.message);
            }
        });

        // 快速登录
        document.getElementById('quickLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const responseDiv = document.getElementById('loginResponse');

            if (!email || !password) {
                responseDiv.textContent = '请填写邮箱和密码';
                return;
            }

            try {
                responseDiv.textContent = '登录中...';

                const response = await fetch('http://localhost:3000/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        loginType: 'email_password',
                        email: email,
                        password: password
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    const token = data.data.token;
                    localStorage.setItem('token', token);
                    loadTokenFromStorage();
                    alert('登录成功！Token已保存到localStorage');
                } else {
                    alert('登录失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
# right-align [![NPM version](https://badge.fury.io/js/right-align.svg)](http://badge.fury.io/js/right-align)

> Right-align the text in a string.

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i right-align --save
```

## Usage

```js
var rightAlign = require('right-align');
rightAlign(string);
```

**Example**

If used on the following:

```
Lorem ipsum dolor sit amet,
consectetur adipiscing
elit, sed do eiusmod tempor incididunt
ut labore et dolore
magna aliqua. Ut enim ad minim
veniam, quis
```

The result would be:

```
           Lorem ipsum dolor sit amet,
                consectetur adipiscing
elit, sed do eiusmod tempor incididunt
                   ut labore et dolore
        magna aliqua. Ut enim ad minim
                          veniam, quis
```

## Related projects

* [align-text](https://github.com/jonschlinkert/align-text): Align the text in a string.
* [center-align](https://github.com/jonschlinkert/center-align): Center-align the text in a string.
* [justify](https://github.com/bahamas10/node-justify): Left or right (or both) justify text using a custom width and character
* [repeat-string](https://github.com/jonschlinkert/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string.
* [repeat-element](https://github.com/jonschlinkert/repeat-element): Create an array by repeating the given value n times.
* [word-wrap](https://github.com/jonschlinkert/word-wrap): Wrap words to a specified length.

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/right-align/issues/new)

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2015 Jon Schlinkert
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on June 09, 2015._

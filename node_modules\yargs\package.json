{"name": "yargs", "version": "3.10.0", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "files": ["index.js", "lib", "completion.sh.hbs", "LICENSE"], "dependencies": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}, "devDependencies": {"chai": "^2.2.0", "coveralls": "^2.11.2", "hashish": "0.0.4", "mocha": "^2.2.1", "nyc": "^2.2.1", "standard": "^3.11.1"}, "scripts": {"test": "standard && nyc mocha --check-leaks && nyc report", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "http://github.com/bcoe/yargs.git"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://CodeTunnel.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/bcoe"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://chrisneedham.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nylen"}, {"name": "<PERSON>", "url": "https://github.com/fizker"}, {"name": "<PERSON>", "url": "https://github.com/linclark"}], "license": "MIT", "engine": {"node": ">=0.4"}}
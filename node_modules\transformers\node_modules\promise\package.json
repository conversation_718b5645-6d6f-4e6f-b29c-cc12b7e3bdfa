{"name": "promise", "version": "2.0.0", "description": "Bare bones Promises/A+ implementation", "main": "index.js", "scripts": {"test": "mocha -R spec --timeout 200 --slow 99999"}, "repository": {"type": "git", "url": "https://github.com/then/promise.git"}, "author": "ForbesLindesay", "license": "MIT", "dependencies": {"is-promise": "~1"}, "devDependencies": {"promises-aplus-tests": "*", "mocha-as-promised": "~1.2.1", "better-assert": "~1.0.0"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试发送验证码接口</title>
</head>
<body>
    <h1>测试发送验证码接口</h1>

    <form id="emailForm">
        <div>
            <label for="email">邮箱地址:</label>
            <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
        </div>
        <br>
        <button type="submit">发送验证码</button>
    </form>

    <div id="result">
        <h3>响应结果:</h3>
        <pre id="response"></pre>
    </div>

    <script>
        document.getElementById('emailForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const responseDiv = document.getElementById('response');

            if (!email) {
                responseDiv.textContent = '请输入邮箱地址';
                return;
            }

            try {
                responseDiv.textContent = '发送中...';

                const response = await fetch('http://localhost:3000/email/getCode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        qq: email
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('验证码发送成功！验证码: ' + data.data.code);
                } else {
                    alert('发送失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试验证码和注册接口</title>
</head>
<body>
    <h1>测试验证码和注册接口</h1>

    <!-- 发送验证码 -->
    <div>
        <h2>1. 发送验证码</h2>
        <form id="emailForm">
            <div>
                <label for="email">邮箱地址:</label>
                <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
            </div>
            <br>
            <button type="submit">发送验证码</button>
        </form>

        <div id="emailResult">
            <h3>验证码响应结果:</h3>
            <pre id="emailResponse"></pre>
        </div>
    </div>

    <hr>

    <!-- 用户注册 -->
    <div>
        <h2>2. 用户注册</h2>
        <form id="registerForm">
            <div>
                <label for="regEmail">邮箱地址:</label>
                <input type="email" id="regEmail" name="regEmail" placeholder="请输入邮箱地址" required>
            </div>
            <br>
            <div>
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" placeholder="请输入密码(至少6位)" required>
            </div>
            <br>
            <div>
                <label for="code">验证码:</label>
                <input type="text" id="code" name="code" placeholder="请输入验证码" required>
            </div>
            <br>
            <button type="submit">注册</button>
        </form>

        <div id="registerResult">
            <h3>注册响应结果:</h3>
            <pre id="registerResponse"></pre>
        </div>
    </div>

    <script>
        // 发送验证码
        document.getElementById('emailForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const responseDiv = document.getElementById('emailResponse');

            if (!email) {
                responseDiv.textContent = '请输入邮箱地址';
                return;
            }

            try {
                responseDiv.textContent = '发送中...';

                const response = await fetch('http://localhost:3000/email/getCode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        qq: email
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('验证码发送成功！验证码: ' + data.data.code);
                    // 自动填充注册表单的邮箱
                    document.getElementById('regEmail').value = email;
                } else {
                    alert('发送失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });

        // 用户注册
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('password').value;
            const code = document.getElementById('code').value;
            const responseDiv = document.getElementById('registerResponse');

            if (!email || !password || !code) {
                responseDiv.textContent = '请填写所有字段';
                return;
            }

            if (password.length < 6) {
                responseDiv.textContent = '密码长度不能少于6位';
                return;
            }

            try {
                responseDiv.textContent = '注册中...';

                const response = await fetch('http://localhost:3000/user/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        code: code
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('注册成功！用户名: ' + data.data.username);
                    // 清空表单
                    document.getElementById('registerForm').reset();
                } else {
                    alert('注册失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
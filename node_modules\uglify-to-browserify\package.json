{"name": "uglify-to-browserify", "version": "1.0.2", "description": "A transform to make UglifyJS work in browserify.", "keywords": [], "dependencies": {}, "devDependencies": {"uglify-js": "~2.4.0", "source-map": "~0.1.27"}, "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/uglify-to-browserify.git"}, "author": "ForbesLindesay", "license": "MIT"}
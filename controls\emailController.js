const nodemailer = require('nodemailer');
const { saveVerificationCode, verifyCode } = require('../utils/sqliteHelper');

// 1. 配置邮件传输器(Transporter)
const transporter = nodemailer.createTransport({
    host: 'smtp.qq.com',       // 使用QQ邮箱的SMTP服务器
    secureConnection: true,     // 使用SSL安全连接
    port: 465,                 // SMTP端口号(SSL)
    secure: true,               // 使用安全连接
    auth: {
        user: '<EMAIL>',  // 发件人邮箱
        pass: 'gspipdsfqjywbcag'    // SMTP授权码
    }
});

// 获取验证码业务逻辑
const getCode = async (req, res) => {
    const { qq } = req.body;
    
    // 验证邮箱参数
    if (!qq) {
        return res.json({
            code: 400,
            message: '邮箱地址不能为空'
        });
    }
    
    // 2. 生成6位随机验证码
    const number = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    
    // 3. 配置邮件内容
    const mailOptions = {
        from: '<EMAIL>',  // 发件人
        to: qq,                     // 收件人(从请求参数获取)
        subject: '书适圈',          // 邮件主题
        text: `                     // 邮件正文(纯文本格式)
        这是您的验证码:
        ${number}
        请尽快进行验证。
        此邮件为系统邮件，请勿回复。
        `
    };
    
    // 4. 发送邮件
    transporter.sendMail(mailOptions, async (err, info) => {
        if (err) {
            console.error('邮件发送失败:', err);
            res.json({
                code: 500,
                message: '邮件发送失败',
                error: err.message
            });
        } else {
            console.log('邮件发送成功:', info.response);

            // 5. 保存验证码到数据库
            try {
                await saveVerificationCode(qq, number, 5); // 5分钟过期
                res.json({
                    code: 200,
                    message: '验证码发送成功',
                    data: {
                        code: number,
                        email: qq
                    }
                });
            } catch (dbErr) {
                console.error('保存验证码失败:', dbErr);
                res.json({
                    code: 500,
                    message: '验证码发送成功，但保存失败',
                    error: dbErr.message
                });
            }
        }
    });
};

// 验证验证码业务逻辑
const checkCode = async (req, res) => {
    const { qq, code } = req.body;

    // 验证参数
    if (!qq || !code) {
        return res.json({
            code: 400,
            message: '邮箱地址和验证码不能为空'
        });
    }

    try {
        const result = await verifyCode(qq, code);

        if (result.success) {
            res.json({
                code: 200,
                message: result.message,
                data: {
                    email: qq,
                    verified: true
                }
            });
        } else {
            res.json({
                code: 400,
                message: result.message,
                data: {
                    email: qq,
                    verified: false
                }
            });
        }
    } catch (error) {
        console.error('验证验证码失败:', error);
        res.json({
            code: 500,
            message: '验证失败',
            error: error.message
        });
    }
};

module.exports = {
    getCode,
    checkCode
};

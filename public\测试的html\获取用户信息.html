<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试获取用户信息接口</title>
</head>
<body>
    <h1>测试获取用户信息接口</h1>

    <!-- 显示当前存储的token -->
    <div>
        <h2>当前存储的Token</h2>
        <div>
            <label>localStorage中的token:</label>
            <br>
            <textarea id="currentToken" rows="3" cols="80" readonly></textarea>
            <br><br>
            <button onclick="loadTokenFromStorage()">从localStorage加载Token</button>
            <button onclick="clearToken()">清除Token</button>
        </div>
    </div>

    <hr>

    <!-- 手动输入token -->
    <div>
        <h2>手动输入Token测试</h2>
        <form id="getUserInfoForm">
            <div>
                <label for="tokenInput">Token:</label>
                <br>
                <textarea id="tokenInput" name="tokenInput" rows="3" cols="80" placeholder="请输入JWT token" required></textarea>
            </div>
            <br>
            <button type="submit">获取用户信息</button>
            <button type="button" onclick="useCurrentToken()">使用当前存储的Token</button>
        </form>

        <div id="result">
            <h3>响应结果:</h3>
            <pre id="response"></pre>
        </div>
    </div>

    <hr>

    <!-- 快速登录获取token -->
    <div>
        <h2>快速登录获取Token（用于测试）</h2>
        <form id="quickLoginForm">
            <div>
                <label for="loginEmail">邮箱:</label>
                <input type="email" id="loginEmail" name="loginEmail" placeholder="请输入邮箱" required>
            </div>
            <br>
            <div>
                <label for="loginPassword">密码:</label>
                <input type="password" id="loginPassword" name="loginPassword" placeholder="请输入密码" required>
            </div>
            <br>
            <button type="submit">邮箱密码登录</button>
        </form>

        <div id="loginResult">
            <h3>登录响应结果:</h3>
            <pre id="loginResponse"></pre>
        </div>
    </div>

    <script>
        // 页面加载时显示当前token
        window.onload = function() {
            loadTokenFromStorage();
        };

        // 从localStorage加载token
        function loadTokenFromStorage() {
            const token = localStorage.getItem('token');
            const currentTokenTextarea = document.getElementById('currentToken');

            if (token) {
                currentTokenTextarea.value = token;
            } else {
                currentTokenTextarea.value = '暂无存储的token';
            }
        }

        // 清除token
        function clearToken() {
            localStorage.removeItem('token');
            document.getElementById('currentToken').value = '暂无存储的token';
            document.getElementById('tokenInput').value = '';
            alert('Token已清除');
        }

        // 使用当前存储的token
        function useCurrentToken() {
            const token = localStorage.getItem('token');
            if (token) {
                document.getElementById('tokenInput').value = token;
            } else {
                alert('暂无存储的token，请先登录');
            }
        }

        // 获取用户信息
        document.getElementById('getUserInfoForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const token = document.getElementById('tokenInput').value.trim();
            const responseDiv = document.getElementById('response');

            if (!token) {
                responseDiv.textContent = '请输入token';
                return;
            }

            try {
                responseDiv.textContent = '获取中...';

                const response = await fetch('http://localhost:3000/user/getUserInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: token
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    alert('获取用户信息成功！用户名: ' + data.data.username);
                } else {
                    alert('获取失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });

        // 快速登录
        document.getElementById('quickLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const responseDiv = document.getElementById('loginResponse');

            if (!email || !password) {
                responseDiv.textContent = '请填写邮箱和密码';
                return;
            }

            try {
                responseDiv.textContent = '登录中...';

                const response = await fetch('http://localhost:3000/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        loginType: 'email_password',
                        email: email,
                        password: password
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                if (data.code === 200) {
                    const token = data.data.token;
                    localStorage.setItem('token', token);
                    loadTokenFromStorage();
                    alert('登录成功！Token已保存到localStorage');
                } else {
                    alert('登录失败: ' + data.message);
                }

            } catch (error) {
                responseDiv.textContent = '请求失败: ' + error.message;
                alert('请求失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
var express = require('express');
var router = express.Router();
const userController = require('../controls/userController');

// 用户注册路由
router.post('/register', userController.register);

// 用户登录路由
router.post('/login', userController.login);

// 获取用户信息路由
router.post('/getUserInfo', userController.getUserInfo);

// 更新头像路由
router.post('/updateAvatar', userController.updateAvatar);

module.exports = router;

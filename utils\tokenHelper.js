const jwt = require('jsonwebtoken');

// JWT配置
const JWT_SECRET = 'hnkj';
const JWT_EXPIRES_IN = '7d'; // 7天

/**
 * 生成JWT token
 * @param {Object} payload - 用户信息
 * @param {number} payload.id - 用户ID
 * @param {string} payload.username - 用户名
 * @param {string} payload.email - 邮箱
 * @returns {string} JWT token
 */
function generateToken(payload) {
    try {
        const { id, username, email } = payload;
        
        // 验证必要参数
        if (!id || !username || !email) {
            throw new Error('缺少必要的用户信息');
        }
        
        // 生成token
        const token = jwt.sign(
            {
                id: id,
                username: username,
                email: email,
                iat: Math.floor(Date.now() / 1000) // 签发时间
            },
            JWT_SECRET,
            {
                expiresIn: JWT_EXPIRES_IN
            }
        );
        
        return token;
    } catch (error) {
        throw new Error('生成token失败: ' + error.message);
    }
}

/**
 * 验证JWT token
 * @param {string} token - JWT token
 * @returns {Object} 解码后的用户信息
 */
function verifyToken(token) {
    try {
        if (!token) {
            throw new Error('token不能为空');
        }
        
        // 移除Bearer前缀（如果有）
        if (token.startsWith('Bearer ')) {
            token = token.slice(7);
        }
        
        // 验证并解码token
        const decoded = jwt.verify(token, JWT_SECRET);
        
        return {
            success: true,
            data: {
                id: decoded.id,
                username: decoded.username,
                email: decoded.email,
                iat: decoded.iat,
                exp: decoded.exp
            }
        };
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            return {
                success: false,
                message: 'token已过期'
            };
        } else if (error.name === 'JsonWebTokenError') {
            return {
                success: false,
                message: 'token无效'
            };
        } else {
            return {
                success: false,
                message: '验证token失败: ' + error.message
            };
        }
    }
}

/**
 * 刷新token（生成新的token）
 * @param {string} oldToken - 旧的JWT token
 * @returns {string} 新的JWT token
 */
function refreshToken(oldToken) {
    try {
        const verifyResult = verifyToken(oldToken);
        
        if (!verifyResult.success) {
            throw new Error(verifyResult.message);
        }
        
        // 使用旧token中的用户信息生成新token
        const { id, username, email } = verifyResult.data;
        return generateToken({ id, username, email });
    } catch (error) {
        throw new Error('刷新token失败: ' + error.message);
    }
}

/**
 * 获取token剩余有效时间（秒）
 * @param {string} token - JWT token
 * @returns {number} 剩余秒数，-1表示已过期或无效
 */
function getTokenRemainingTime(token) {
    try {
        const verifyResult = verifyToken(token);
        
        if (!verifyResult.success) {
            return -1;
        }
        
        const now = Math.floor(Date.now() / 1000);
        const exp = verifyResult.data.exp;
        
        return Math.max(0, exp - now);
    } catch (error) {
        return -1;
    }
}

module.exports = {
    generateToken,
    verifyToken,
    refreshToken,
    getTokenRemainingTime
};
